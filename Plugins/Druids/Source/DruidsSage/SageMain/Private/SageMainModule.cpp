#include "SageMainModule.h"

#include "ChatRequestHandler_V2.h"
#include "DruidsSageEditorModule.h"
#include "SageExtensionDelegator.h"
#include "SDruidsSageChatShell.h"

#define LOCTEXT_NAMESPACE "FSageMainModule"

void FSageMainModule::StartupModule()
{
	// Register for post-engine init callback
	FCoreDelegates::OnPostEngineInit.AddRaw(this, &FSageMainModule::OnPostEngineInit);
}

void FSageMainModule::ShutdownModule()
{
	if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
	{
		FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");
		EditorModule.OnCreateChatShell.Unbind();
	}

	FCoreDelegates::OnPostEngineInit.RemoveAll(this);
}

void FSageMainModule::OnPostEngineInit()
{
	if (FModuleManager::Get().IsModuleLoaded("DruidsSageEditorModule"))
	{
		FDruidsSageEditorModule& EditorModule = FModuleManager::GetModuleChecked<FDruidsSageEditorModule>("DruidsSageEditorModule");
        
		// Bind a lambda to the OnCreateChatShell event
		EditorModule.OnCreateChatShell.BindLambda([this]() -> TSharedPtr<SCompoundWidget> {
			TSharedPtr<SDruidsSageChatShell> ChatShell = SNew(SDruidsSageChatShell)
				.ChatRequestHandler(MakeShared<ChatRequestHandler_V2>())
				.ExtensionDelegator(MakeShared<USageExtensionDelegator>())
			;

			return ChatShell;
		});
	}
}

#undef LOCTEXT_NAMESPACE
    
IMPLEMENT_MODULE(FSageMainModule, SageMain)