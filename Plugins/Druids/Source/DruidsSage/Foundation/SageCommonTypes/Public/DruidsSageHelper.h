#pragma once

#include <CoreMinimal.h>
#include <Kismet/BlueprintFunctionLibrary.h>

#include "DruidsSageChatTypes.h"

#include "DruidsSageHelper.generated.h"

/**
 *
 */
UCLASS(NotPlaceable, Category = "DruidsSage")
class SAGECOMMONTYPES_API UDruidsSageHelper final : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintPure, Category = "DruidsSage | Chat", meta = (DisplayName = "Convert DruidsSage Role to Name"))
	static const FName RoleToName(const EDruidsSageChatRole Role);

	UFUNCTION(BlueprintPure, Category = "DruidsSage | Chat", meta = (DisplayName = "Convert Name to DruidsSage Role"))
	static const EDruidsSageChatRole NameToRole(const FName Role);

	UFUNCTION(BlueprintPure, Category = "DruidsSage | Chat", meta = (DisplayName = "Load Options From Ini"))
	static const FDruidsSageCommonOptions LoadCommonOptionsFromIni();

	UFUNCTION(BlueprintCallable, Category = "DruidsSage | Chat", meta = (DisplayName = "Save Window Options To Ini"))
	static void SaveWindowOptionsToIni(const FDruidsSageCommonOptions& Options);
};
