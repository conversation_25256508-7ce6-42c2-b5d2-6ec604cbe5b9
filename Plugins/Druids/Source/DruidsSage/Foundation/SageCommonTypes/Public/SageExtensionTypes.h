#pragma once

#include <CoreMinimal.h>

#include "SageExtensionTypes.generated.h"

UENUM(BlueprintType, meta = (DisplayName = "Sage Extension Context Type"))
enum class EDruidsSageExtensionContextType : uint8
{
    Any,
    AssetEditor,
//    Tab,  //TODO: Add support for tab types
};

UENUM(BlueprintType, meta = (DisplayName = "Sage Extension Parmeter Type"))
enum class EDruidsSageExtensionParameterType : uint8
{
    None,
    String,
    Int,
    Float,
    Bool,
    Vector3,
    Color,
    Rotator,
    LinearColor,
    Enum,
    Array,
    Transform,
    CustomStruct,
};

USTRUCT(BlueprintType, meta = (DisplayName = "Sage Parameter Definition"))
struct SAGECOMMONTYPES_API FDruidsSageExtensionParameterDefinition
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension", Meta = (DisplayName = "Name"))
    FName Name = NAME_None;
    
    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension", Meta = (DisplayName = "Type"))
    EDruidsSageExtensionParameterType ParameterType = EDruidsSageExtensionParameterType::None;

    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension", 
        Meta = (DisplayName = "Array Element Type", EditCondition = "ParameterType == EDruidsSageExtensionParameterType::Array"))
    EDruidsSageExtensionParameterType ArrayElementType = EDruidsSageExtensionParameterType::None;

    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension", 
    Meta = (DisplayName = "Struct Element Type", EditCondition = "ParameterType == EDruidsSageExtensionParameterType::CustomStruct"))
    TWeakObjectPtr<UScriptStruct> StructElementType;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Sage Extension",
        Meta = (DisplayName = "Description", MultiLine = "true", ScrollBarAlwaysVisible = "true"))
    FString Description;

public:
    static EDruidsSageExtensionParameterType GetParameterTypeForProperty(const FProperty* Property);
};

USTRUCT(BlueprintType, meta = (DisplayName = "Sage Extension Action Definition"))
struct SAGECOMMONTYPES_API FDruidsSageExtensionActionDefinition
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension", Meta = (DisplayName = "Function Name"))
    FName FunctionName = NAME_None;

    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension",
        Meta = (DisplayName = "Description", MultiLine = "true"))
    FString Description;

    UPROPERTY(BlueprintReadOnly, EditAnywhere, Category = "Sage Extension",
        Meta = (DisplayName = "Parameters"))
    TArray<FDruidsSageExtensionParameterDefinition> ParameterDefinitions;
};

USTRUCT(BlueprintType, meta = (DisplayName = "Sage Extension Query Definition"))
struct SAGECOMMONTYPES_API FDruidsSageExtensionQueryDefinition
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension", Meta = (DisplayName = "Function Name"))
    FName FunctionName = NAME_None;

    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension",
        Meta = (DisplayName = "Description", MultiLine = "true"))
    FString Description;

    UPROPERTY(BlueprintReadOnly, EditAnywhere, Category = "Sage Extension",
        Meta = (DisplayName = "Parameter Definitions", EditFixedSize = "true"))
    TArray<FDruidsSageExtensionParameterDefinition> ParameterDefinitions;

    UPROPERTY(BlueprintReadOnly, EditAnywhere, Category = "Sage Extension",
        Meta = (DisplayName = "Result Definitions", EditFixedSize = "true"))
    TArray<FDruidsSageExtensionParameterDefinition> ResultDefinitions;
};

USTRUCT(BlueprintType)
struct SAGECOMMONTYPES_API FDruidsSageExtensionAssetTypeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sage Extension")
    FString DisplayName;

    UPROPERTY()
    FString ModuleName;

    UPROPERTY()
    FString ClassName;

    FDruidsSageExtensionAssetTypeData() {}
    FDruidsSageExtensionAssetTypeData(const FString& InDisplayName, const FString& InModuleName, const FString& InClassName)
        : DisplayName(InDisplayName), ModuleName(InModuleName), ClassName(InClassName) {}
};

USTRUCT(BlueprintType)
struct SAGECOMMONTYPES_API FDruidsSageExtensionDefinition
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, VisibleAnywhere, Category = "Sage Extension", Meta = (DisplayName = "Extension Id"))
    FString ExtensionId;
    
    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Sage Extension", Meta = (DisplayName = "Extension Name"))
    FString ExtensionName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Sage Extension",
        Meta = (DisplayName = "Extension Description", MultiLine = "true"))
    FString ExtensionDescription;
    
    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Sage Extension", Meta = (DisplayName = "Context Type"))
    EDruidsSageExtensionContextType ContextType = EDruidsSageExtensionContextType::Any;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Sage Extension",
        Meta = (DisplayName = "Asset Type",
               EditCondition = "ContextType == EDruidsSageExtensionContextType::AssetEditor",
               GetOptions = "GetAvailableAssetTypes"))
    FString SelectedAssetType;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Sage Extension", Meta = (DisplayName = "Actions"))
    TArray<FDruidsSageExtensionActionDefinition> Actions;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Sage Extension", Meta = (DisplayName = "Queries"))
    TArray<FDruidsSageExtensionQueryDefinition> Queries;

    /** Returns the extension definition as a JSON object */
    TSharedPtr<FJsonObject> GetExtensionDefinitionJson() const;

private:
    TSharedPtr<FJsonObject> GetStructTypeInfoAsJson(TWeakObjectPtr<UScriptStruct> StructType) const;
};
