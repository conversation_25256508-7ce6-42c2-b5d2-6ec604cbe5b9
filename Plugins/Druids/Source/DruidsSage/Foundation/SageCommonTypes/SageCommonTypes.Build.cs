using UnrealBuildTool;

public class SageCommonTypes : ModuleRules
{
    public SageCommonTypes(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                
                "UnrealEd",
                
                "Json",
            }
        );
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",
            }
        );
    }
}