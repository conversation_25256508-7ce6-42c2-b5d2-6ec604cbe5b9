#pragma once

#include "CoreMinimal.h"
#include "Serialization/JsonSerializer.h"
#include "BlueprintIndexTypes.generated.h"

/**
 * Enum for Blueprint node types
 */
UENUM(BlueprintType)
enum class EBlueprintNodeType : uint8
{
    Function,
    Event,
    Variable,
    Component,
    Other
};

/**
 * Struct for Blueprint node reference
 */
USTRUCT(BlueprintType)
struct SAGEBLUEPRINTUTILITIES_API FBlueprintNodeReference
{
    GENERATED_BODY()

    FBlueprintNodeReference()
        : NodeType()
    {
    }
    
    /** The Blueprint name */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString BlueprintName;

    /** The node type */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    EBlueprintNodeType NodeType;

    /** The node name */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString NodeName;

    /** The node GUID */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString NodeGuid;

    /** Path to the node JSON file */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString DetailsPath;

    /** Path to the Blueprint JSON file */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString BlueprintDetailsPath;
};

/**
 * Struct for Blueprint search result
 */
USTRUCT(BlueprintType)
struct SAGEBLUEPRINTUTILITIES_API FBlueprintSearchResult
{
    GENERATED_BODY()

    /** The search results */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    TArray<FBlueprintNodeReference> Results;
};

/**
 * Struct for Blueprint node details
 */
USTRUCT(BlueprintType)
struct SAGEBLUEPRINTUTILITIES_API FBlueprintNodeDetails
{
    GENERATED_BODY()

    FBlueprintNodeDetails()
        : NodeType(),
          NodePosition()
    {
    }
    
    /** The node GUID */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString NodeGuid;

    /** The node title */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString NodeTitle;

    /** The node type */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString NodeType;

    /** The node description (for Functions/Events) */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString Description;

    /** The node position */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FVector2D NodePosition;

    /** The component class (for Components) */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString ComponentClass;

    /** The parent attachment (for Components) */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString ParentAttachment;
};

/**
 * Struct for Blueprint details
 */
USTRUCT(BlueprintType)
struct SAGEBLUEPRINTUTILITIES_API FBlueprintDetails
{
    GENERATED_BODY()

    /** The Blueprint name */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString BlueprintName;

    /** The Blueprint path */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString Path;

    /** The Blueprint class */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString Class;

    /** The last modified timestamp */
    UPROPERTY(BlueprintReadOnly, Category = "Blueprint Search")
    FString LastModified;
};

/**
 * Helper class for Blueprint index types
 */
class SAGEBLUEPRINTUTILITIES_API FBlueprintIndexTypes
{
public:
    /**
     * Converts a node type enum to string
     */
    static FString NodeTypeToString(EBlueprintNodeType NodeType);

    /**
     * Converts a string to node type enum
     */
    static EBlueprintNodeType StringToNodeType(const FString& NodeTypeString);

    /**
     * Creates a JSON object from a Blueprint node reference
     */
    static TSharedPtr<FJsonObject> NodeReferenceToJson(const FBlueprintNodeReference& NodeReference);

    /**
     * Creates a Blueprint node reference from a JSON object
     */
    static FBlueprintNodeReference JsonToNodeReference(const TSharedPtr<FJsonObject>& JsonObject);

    /**
     * Creates a JSON object from a Blueprint search result
     */
    static TSharedPtr<FJsonObject> SearchResultToJson(const FBlueprintSearchResult& SearchResult);

    /**
     * Creates a Blueprint search result from a JSON object
     */
    static FBlueprintSearchResult JsonToSearchResult(const TSharedPtr<FJsonObject>& JsonObject);
};
